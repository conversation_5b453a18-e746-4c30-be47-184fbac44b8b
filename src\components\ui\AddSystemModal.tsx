"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Locale } from "@/i18n-config";
import { X, Plus, User, Mail, Database, Building, UserCheck } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SystemsService } from "@/Firebase/firestore/SystemsService";
import { UserProfile } from "@/Firebase/firestore/services/UserService";

interface AddSystemModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (systemData: {
    name: string;
    responsibleOwner: string;
    dba: string;
    email: string;
    group?: string;
    consultantId?: string;
    consultantName?: string;
    consultantEmail?: string;
  }) => void;
  lang: Locale;
  isLoading?: boolean;
}

export function AddSystemModal({
  isOpen,
  onClose,
  onSubmit,
  lang,
  isLoading = false
}: AddSystemModalProps) {
  const isRTL = lang === "ar";
  const [formData, setFormData] = useState({
    name: "",
    responsibleOwner: "",
    dba: "",
    email: "",
    group: "",
    consultantId: "no-consultant",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [consultants, setConsultants] = useState<UserProfile[]>([]);
  const [loadingConsultants, setLoadingConsultants] = useState(false);

  // Load consultants when modal opens
  useEffect(() => {
    if (isOpen) {
      loadConsultants();
    }
  }, [isOpen]);

  const loadConsultants = async () => {
    try {
      setLoadingConsultants(true);
      const consultantsList = await SystemsService.getConsultants();
      setConsultants(consultantsList);
    } catch (error) {
      console.error('Error loading consultants:', error);
    } finally {
      setLoadingConsultants(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = isRTL ? "اسم النظام مطلوب" : "System name is required";
    }

    if (!formData.responsibleOwner.trim()) {
      newErrors.responsibleOwner = isRTL ? "المسؤول مطلوب" : "Responsible owner is required";
    }

    if (!formData.dba.trim()) {
      newErrors.dba = isRTL ? "مدير قاعدة البيانات مطلوب" : "DBA is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = isRTL ? "البريد الإلكتروني مطلوب" : "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = isRTL ? "البريد الإلكتروني غير صحيح" : "Invalid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      const selectedConsultant = consultants.find(c => c.uid === formData.consultantId);
      const submitData: {
        name: string;
        responsibleOwner: string;
        dba: string;
        email: string;
        group?: string;
        consultantId?: string;
        consultantName?: string;
        consultantEmail?: string;
      } = {
        name: formData.name,
        responsibleOwner: formData.responsibleOwner,
        dba: formData.dba,
        email: formData.email,
      };

      // Only add fields that have values
      if (formData.group) {
        submitData.group = formData.group;
      }
      if (formData.consultantId && formData.consultantId !== "no-consultant" && selectedConsultant) {
        submitData.consultantId = formData.consultantId;
        submitData.consultantName = selectedConsultant.displayName || undefined;
        submitData.consultantEmail = selectedConsultant.email || undefined;
      }

      onSubmit(submitData);
      setFormData({ name: "", responsibleOwner: "", dba: "", email: "", group: "", consultantId: "no-consultant" });
      setErrors({});
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const handleClose = () => {
    setFormData({ name: "", responsibleOwner: "", dba: "", email: "", group: "", consultantId: "no-consultant" });
    setErrors({});
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className={`relative bg-white rounded-2xl shadow-2xl w-full max-w-md mx-4 overflow-hidden ${isRTL ? "rtl" : "ltr"}`}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-dark-gray)] px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <Plus className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-xl font-bold text-white">
                    {isRTL ? "إضافة نظام جديد" : "Add New System"}
                  </h2>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="text-white hover:bg-white/20 p-2"
                  disabled={isLoading}
                >
                  <X size={20} />
                </Button>
              </div>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* System Name */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Building className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "اسم النظام" : "System Name"}
                </Label>
                <Input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className={`${errors.name ? "border-red-500" : ""}`}
                  placeholder={isRTL ? "أدخل اسم النظام" : "Enter system name"}
                  disabled={isLoading}
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              {/* Responsible Owner */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <User className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "المسؤول من ثقة" : "Responsible Owner from Thiqah"}
                </Label>
                <Input
                  type="text"
                  value={formData.responsibleOwner}
                  onChange={(e) => handleInputChange("responsibleOwner", e.target.value)}
                  className={`${errors.responsibleOwner ? "border-red-500" : ""}`}
                  placeholder={isRTL ? "أدخل اسم المسؤول" : "Enter responsible owner name"}
                  disabled={isLoading}
                />
                {errors.responsibleOwner && (
                  <p className="text-sm text-red-600">{errors.responsibleOwner}</p>
                )}
              </div>

              {/* DBA */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Database className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "مدير قاعدة البيانات" : "Database Administrator (DBA)"}
                </Label>
                <Input
                  type="text"
                  value={formData.dba}
                  onChange={(e) => handleInputChange("dba", e.target.value)}
                  className={`${errors.dba ? "border-red-500" : ""}`}
                  placeholder={isRTL ? "أدخل اسم مدير قاعدة البيانات" : "Enter DBA name"}
                  disabled={isLoading}
                />
                {errors.dba && (
                  <p className="text-sm text-red-600">{errors.dba}</p>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Mail className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "البريد الإلكتروني" : "Email Address"}
                </Label>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className={`${errors.email ? "border-red-500" : ""}`}
                  placeholder={isRTL ? "أدخل البريد الإلكتروني" : "Enter email address"}
                  disabled={isLoading}
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              {/* Group */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Building className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "المجموعة" : "Group"}
                </Label>
                <select
                  value={formData.group}
                  onChange={(e) => handleInputChange("group", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
                  disabled={isLoading}
                >
                  <option value="">{isRTL ? "اختر المجموعة" : "Select Group"}</option>
                  {Array.from({ length: 9 }, (_, i) => i + 1).map(num => (
                    <option key={num} value={num.toString()}>
                      {isRTL ? `المجموعة ${num}` : `Group ${num}`}
                    </option>
                  ))}
                </select>
              </div>

              {/* Consultant */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <UserCheck className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "المستشار المسؤول" : "Assigned Consultant"}
                </Label>
                <Select
                  value={formData.consultantId}
                  onValueChange={(value) => handleInputChange("consultantId", value)}
                  disabled={isLoading || loadingConsultants}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={
                      loadingConsultants
                        ? (isRTL ? "جاري التحميل..." : "Loading...")
                        : (isRTL ? "اختر المستشار" : "Select Consultant")
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-consultant">
                      {isRTL ? "بدون مستشار" : "No Consultant"}
                    </SelectItem>
                    {consultants.map((consultant) => (
                      <SelectItem key={consultant.uid} value={consultant.uid}>
                        <div className="flex items-center gap-2">
                          <span>{consultant.displayName || consultant.email}</span>
                          {consultant.email && consultant.displayName && (
                            <span className="text-xs text-gray-500">({consultant.email})</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Submit Button */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  className="flex-1"
                  disabled={isLoading}
                >
                  {isRTL ? "إلغاء" : "Cancel"}
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                  disabled={isLoading}
                >
                  {isLoading 
                    ? (isRTL ? "جاري الحفظ..." : "Saving...") 
                    : (isRTL ? "حفظ النظام" : "Save System")
                  }
                </Button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
} 