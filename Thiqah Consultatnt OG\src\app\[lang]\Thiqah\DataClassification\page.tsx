"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { Locale } from "@/i18n-config";
import { Plus, Shield, Database, Users, BookOpen, Scale, FileText, Gavel, Edit, Trash2, User, Mail, Building, Save, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemCard } from "@/components/ui/SystemCard";
import { AddSystemModal } from "@/components/ui/AddSystemModal";
import { SystemsService, System } from "@/Firebase/firestore/SystemsService";
import { PDPLService, PDPLDocumentType } from "@/Firebase/firestore/services/PDPLService";
import { UserProfile } from "@/Firebase/firestore/services/UserService";
import { useToast } from "@/components/ui/use-toast";
import { AddPDPLArticleModal } from "@/components/ui/AddPDPLArticleModal";
import { AddPDPLDefinitionModal } from "@/components/ui/AddPDPLDefinitionModal";
import { PDPLArticleCard } from "@/components/ui/PDPLArticleCard";

import { PDPLGlossaryModal } from "@/components/ui/PDPLGlossaryModal";
import { ArticleModal } from "@/components/ui/ArticleModal";
import type { PDPLPoint } from "@/Firebase/firestore/services/PDPLService";
import { Timestamp } from 'firebase/firestore';

interface DataClassificationPageProps {
  params: Promise<{ lang: Locale }>;
}

export default function DataClassificationPage({ params }: DataClassificationPageProps) {
  const [lang, setLang] = useState<Locale>('en');
  const [isInitialized, setIsInitialized] = useState(false);
  const isRTL = lang === "ar";
  const { toast } = useToast();

  // Initialize params
  useEffect(() => {
    params.then(({ lang }) => {
      setLang(lang);
      setIsInitialized(true);
    });
  }, [params]);

  const [systems, setSystems] = useState<System[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState<'systems' | 'pdpl'>('systems');

  // Edit System Modal State
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingSystem, setEditingSystem] = useState<System | null>(null);
  const [editFormData, setEditFormData] = useState({
    name: '',
    responsibleOwner: '',
    dba: '',
    email: '',
    group: '',
    consultantId: ''
  });

  // Delete System Modal State
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deletingSystem, setDeletingSystem] = useState<System | null>(null);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  // Consultant State
  const [consultants, setConsultants] = useState<UserProfile[]>([]);
  const [loadingConsultants, setLoadingConsultants] = useState(false);
  const [activePDPLTab, setActivePDPLTab] = useState<PDPLDocumentType>(PDPLDocumentType.PDPL_LAW);
  
  // PDPL Modal states
  const [isArticleModalOpen, setIsArticleModalOpen] = useState(false);
  const [isDefinitionModalOpen, setIsDefinitionModalOpen] = useState(false);
  const [isGlossaryModalOpen, setIsGlossaryModalOpen] = useState(false);
  const [isSubmittingPDPL, setIsSubmittingPDPL] = useState(false);

  // Article Modal states
  const [isArticleViewModalOpen, setIsArticleViewModalOpen] = useState(false);
  const [selectedArticle, setSelectedArticle] = useState<{
    id?: string;
    articleNumber: string;
    title: string;
    description: string;
    points?: PDPLPoint[];
    linkedArticles?: string[];
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  } | null>(null);
  const [selectedPointId, setSelectedPointId] = useState<string | undefined>(undefined);
  
  // PDPL Data states
  const [pdplArticles, setPdplArticles] = useState<Array<{
    id?: string;
    articleNumber: string;
    title: string;
    description: string;
    points?: PDPLPoint[];
    linkedArticles?: string[];
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  }>>([]);
  const [pdplDefinitions, setPdplDefinitions] = useState<Array<{
    id?: string;
    term: string;
    definition: string;
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  }>>([]);
  const [isLoadingPDPL, setIsLoadingPDPL] = useState(false);

  // Pagination states for performance optimization
  const [currentArticlePage, setCurrentArticlePage] = useState(1);
  const [articlesPerPage] = useState(6); // Reduced from showing all to 6 per page
  const [totalArticles, setTotalArticles] = useState(0);

  // Memoize stats calculation for better performance (moved here to maintain hook order)
  const stats = useMemo(() => [
    {
      title: isRTL ? "إجمالي الأنظمة" : "Total Systems",
      value: systems.length,
      icon: <Database className="w-6 h-6" />,
    },
    {
      title: isRTL ? "المسؤولون" : "Responsible Owners",
      value: new Set(systems.map(s => s.responsibleOwner)).size,
      icon: <Users className="w-6 h-6" />,
    },
    {
      title: isRTL ? "مديرو قواعد البيانات" : "Database Administrators",
      value: new Set(systems.map(s => s.dba)).size,
      icon: <Shield className="w-6 h-6" />,
    },
  ], [systems, isRTL]); // Only recalculate when systems or language changes

  const loadConsultants = useCallback(async () => {
    try {
      setLoadingConsultants(true);
      const consultantsList = await SystemsService.getConsultants();
      setConsultants(consultantsList);
    } catch (error) {
      console.error('Error loading consultants:', error);
    } finally {
      setLoadingConsultants(false);
    }
  }, []);

  const loadSystems = useCallback(async () => {
    try {
      setIsLoading(true);
      const systemsData = await SystemsService.getSystems();
      setSystems(systemsData);
    } catch {
      toast({
        title: isRTL ? "خطأ في تحميل الأنظمة" : "Error loading systems",
        description: isRTL ? "فشل في تحميل الأنظمة" : "Failed to load systems",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isRTL, toast]);

  const loadPDPLData = useCallback(async (page: number = 1) => {
    try {
      console.log('Loading PDPL data for tab:', activePDPLTab, 'page:', page);
      setIsLoadingPDPL(true);

      // Load articles with pagination for better performance
      const articlesData = await PDPLService.getArticlesByType(activePDPLTab);
      console.log('Loaded articles:', articlesData.length);

      // Sort articles by article number (extract number from articleNumber field)
      const sortedArticles = articlesData.sort((a, b) => {
        // Extract numbers from article numbers (e.g., "Article 1", "المادة 15", "Art. 3")
        const getArticleNumber = (articleNumber: string) => {
          const match = articleNumber.match(/\d+/);
          return match ? parseInt(match[0], 10) : 0;
        };

        const numA = getArticleNumber(a.articleNumber);
        const numB = getArticleNumber(b.articleNumber);

        return numA - numB;
      });

      // Load definitions separately to avoid dependency loop
      const definitionsData = await PDPLService.getAllDefinitions();
      console.log('Loaded definitions:', definitionsData.length);

      // Apply client-side pagination to reduce rendering load
      const startIndex = (page - 1) * articlesPerPage;
      const endIndex = startIndex + articlesPerPage;
      const paginatedArticles = sortedArticles.slice(startIndex, endIndex);

      setPdplArticles(paginatedArticles);
      setPdplDefinitions(definitionsData);
      setTotalArticles(sortedArticles.length);
      setCurrentArticlePage(page);
    } catch (error) {
      console.error('Error loading PDPL data:', error);
      console.error('Error details:', error instanceof Error ? error.message : 'Unknown error');
      toast({
        title: isRTL ? "خطأ في تحميل البيانات" : "Error loading data",
        description: isRTL ? "فشل في تحميل بيانات القانون" : `Failed to load law data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoadingPDPL(false);
    }
  }, [activePDPLTab, isRTL, toast, articlesPerPage]); // Removed pdplDefinitions to prevent infinite loop

  // Load systems on component mount (only after params are initialized)
  useEffect(() => {
    if (isInitialized) {
      loadSystems();
    }
  }, [loadSystems, isInitialized]);

  // Load PDPL data when initialized or when active tab changes
  useEffect(() => {
    if (isInitialized && activeTab === 'pdpl') {
      setCurrentArticlePage(1); // Reset to first page when changing tabs
      loadPDPLData(1);
    }
  }, [isInitialized, activeTab, activePDPLTab, loadPDPLData]); // Simplified dependencies to prevent loops

  // Don't render until params are initialized
  if (!isInitialized) {
    return <div>Loading...</div>;
  }

  const handleAddSystem = async (systemData: {
    name: string;
    responsibleOwner: string;
    dba: string;
    email: string;
    group?: string;
    consultantId?: string;
    consultantName?: string;
    consultantEmail?: string;
  }) => {
    try {
      setIsSubmitting(true);
      await SystemsService.addSystem(systemData);
      await loadSystems(); // Reload systems after adding
      setIsModalOpen(false);
      toast({
        title: isRTL ? "تم إضافة النظام بنجاح" : "System added successfully",
        description: isRTL ? "تم حفظ النظام الجديد" : "New system has been saved",
        variant: "default",
      });
          } catch (error) {
        console.error('Error adding system:', error);
        toast({
          title: isRTL ? "خطأ في إضافة النظام" : "Error adding system",
        description: isRTL ? "فشل في حفظ النظام" : "Failed to save system",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit system
  const handleEditSystem = (system: System) => {
    setEditingSystem(system);
    setEditFormData({
      name: system.name,
      responsibleOwner: system.responsibleOwner,
      dba: system.dba,
      email: system.email,
      group: system.group || '',
      consultantId: system.consultantId || ''
    });
    setEditModalOpen(true);
    // Load consultants when opening edit modal
    loadConsultants();
  };

  // Handle save system edits
  const handleSaveSystemEdit = async () => {
    if (!editingSystem) return;

    try {
      setIsSubmitting(true);

      const selectedConsultant = consultants.find(c => c.uid === editFormData.consultantId);

      const updateData: Partial<System> = {
        name: editFormData.name,
        responsibleOwner: editFormData.responsibleOwner,
        dba: editFormData.dba,
        email: editFormData.email,
      };

      // Only add fields that have values
      if (editFormData.group) {
        updateData.group = editFormData.group;
      }
      if (editFormData.consultantId && selectedConsultant) {
        updateData.consultantId = editFormData.consultantId;
        updateData.consultantName = selectedConsultant.displayName || undefined;
        updateData.consultantEmail = selectedConsultant.email || undefined;
      }

      await SystemsService.updateSystem(editingSystem.id!, updateData);

      // Update local state
      setSystems(prevSystems =>
        prevSystems.map(sys =>
          sys.id === editingSystem.id
            ? { ...sys, ...editFormData }
            : sys
        )
      );

      setEditModalOpen(false);
      setEditingSystem(null);

      toast({
        title: isRTL ? "تم تحديث النظام بنجاح" : "System updated successfully",
        description: isRTL ? "تم حفظ التغييرات بنجاح" : "Changes saved successfully",
      });
    } catch (error) {
      console.error('Error updating system:', error);
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update error",
        description: isRTL ? "فشل في تحديث النظام" : "Failed to update system",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete system - now opens modal instead of direct confirmation
  const handleDeleteSystem = (systemId: string) => {
    const system = systems.find(s => s.id === systemId);
    if (system) {
      setDeletingSystem(system);
      setDeleteConfirmText('');
      setDeleteModalOpen(true);
    }
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!deletingSystem) return;

    const expectedText = `delete ${deletingSystem.name}`;
    if (deleteConfirmText.toLowerCase() !== expectedText.toLowerCase()) {
      toast({
        title: isRTL ? "نص التأكيد غير صحيح" : "Incorrect confirmation text",
        description: isRTL ? `يجب كتابة: ${expectedText}` : `Please type: ${expectedText}`,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsDeleting(true);

      await SystemsService.deleteSystem(deletingSystem.id!);

      // Remove from local state
      setSystems(prevSystems =>
        prevSystems.filter(sys => sys.id !== deletingSystem.id)
      );

      setDeleteModalOpen(false);
      setDeletingSystem(null);
      setDeleteConfirmText('');

      toast({
        title: isRTL ? "تم حذف النظام" : "System deleted",
        description: isRTL ? "تم حذف النظام بنجاح" : "System deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting system:', error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete error",
        description: isRTL ? "فشل في حذف النظام" : "Failed to delete system",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Define main tabs
  const mainTabs = [
    {
      id: 'systems' as const,
      label: isRTL ? "الأنظمة" : "Systems",
      subtitle: isRTL ? "إدارة الأنظمة" : "Systems Management",
      icon: Database
    },
    {
      id: 'pdpl' as const,
      label: isRTL ? "قانون حماية البيانات" : "PDPL Law",
      subtitle: isRTL ? "مواد القانون" : "Law Articles",
      icon: Scale
    }
  ];

  // Define PDPL tabs
  const pdplTabs = [
    {
      id: PDPLDocumentType.PDPL_LAW,
      label: isRTL ? "قانون حماية البيانات الشخصية" : "Personal Data Protection Law",
      subtitle: isRTL ? "المواد والنقاط الفرعية" : "Articles & Sub-points",
      icon: Scale
    },
    {
      id: PDPLDocumentType.IMPLEMENTING_REGULATION,
      label: isRTL ? "اللائحة التنفيذية" : "Implementing Regulation",
      subtitle: isRTL ? "المواد والنقاط الفرعية" : "Articles & Sub-points",
      icon: FileText
    },
    {
      id: PDPLDocumentType.TRANSFER_RULES,
      label: isRTL ? "نقل البيانات خارج المملكة" : "Data Transfer Outside Kingdom",
      subtitle: isRTL ? "المواد والنقاط والنقاط الفرعية" : "Articles, Points & Sub-points",
      icon: Gavel
    }
  ];



  // PDPL handlers
  const handleAddArticle = async (articleData: {
    articleNumber: string;
    title: string;
    documentType: PDPLDocumentType;
    points: PDPLPoint[];
    linkedArticles: string[];
  }) => {
    try {
      setIsSubmittingPDPL(true);
      // Add a default description since articles now only contain points/sub-points
      const articleWithDescription = {
        ...articleData,
        description: articleData.points.length > 0
          ? `This article contains ${articleData.points.length} point${articleData.points.length > 1 ? 's' : ''}.`
          : "This article contains detailed points and sub-points."
      };
      await PDPLService.createArticle(articleWithDescription);
      await loadPDPLData(); // Reload data after adding
      setIsArticleModalOpen(false);
      toast({
        title: isRTL ? "تم إضافة المادة بنجاح" : "Article added successfully",
        description: isRTL ? "تم حفظ المادة الجديدة" : "New article has been saved",
        variant: "default",
      });
    } catch (error) {
      console.error('Error adding article:', error);
      toast({
        title: isRTL ? "خطأ في إضافة المادة" : "Error adding article",
        description: isRTL ? "فشل في حفظ المادة" : "Failed to save article",
        variant: "destructive",
      });
    } finally {
      setIsSubmittingPDPL(false);
    }
  };

  const handleEditArticle = async (article: {
    id?: string;
    articleNumber: string;
    title: string;
    description: string;
    points?: PDPLPoint[];
    linkedArticles?: string[];
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  }) => {
    // TODO: Implement edit article functionality
    console.log('Edit article:', article);
    toast({
      title: isRTL ? "قريباً" : "Coming Soon",
      description: isRTL ? "ميزة تحرير المقالات قيد التطوير" : "Article editing feature is under development",
      variant: "default",
    });
  };

  const handleDeleteArticle = async (articleId: string) => {
    if (!confirm(isRTL ? "هل أنت متأكد من حذف هذه المادة؟" : "Are you sure you want to delete this article?")) {
      return;
    }

    try {
      await PDPLService.deleteArticle(articleId);
      await loadPDPLData(); // Reload data after deletion
      toast({
        title: isRTL ? "تم حذف المادة بنجاح" : "Article deleted successfully",
        description: isRTL ? "تم حذف المادة" : "Article has been deleted",
        variant: "default",
      });
    } catch (error) {
      console.error('Error deleting article:', error);
      toast({
        title: isRTL ? "خطأ في حذف المادة" : "Error deleting article",
        description: isRTL ? "فشل في حذف المادة" : "Failed to delete article",
        variant: "destructive",
      });
    }
  };

  const handleAddDefinition = async (definitionData: {
    term: string;
    definition: string;
  }) => {
    try {
      setIsSubmittingPDPL(true);
      // Create definition without document type - shared across all types
      await PDPLService.createDefinition({
        ...definitionData,
        documentType: PDPLDocumentType.PDPL_LAW // Use a default type for backward compatibility
      });
      await loadPDPLData(); // Reload data after adding
      setIsDefinitionModalOpen(false);
      toast({
        title: isRTL ? "تم إضافة التعريف بنجاح" : "Definition added successfully",
        description: isRTL ? "تم حفظ التعريف الجديد - متاح لجميع أنواع الوثائق" : "New definition has been saved - available for all document types",
        variant: "default",
      });
    } catch (error) {
      console.error('Error adding definition:', error);
      toast({
        title: isRTL ? "خطأ في إضافة التعريف" : "Error adding definition",
        description: isRTL ? "فشل في حفظ التعريف" : "Failed to save definition",
        variant: "destructive",
      });
    } finally {
      setIsSubmittingPDPL(false);
    }
  };

  const handleEditDefinition = async (definitionId: string) => {
    // TODO: Implement edit definition functionality
    console.log('Edit definition:', definitionId);
    toast({
      title: isRTL ? "قريباً" : "Coming Soon",
      description: isRTL ? "ميزة تحرير التعريفات قيد التطوير" : "Definition editing feature is under development",
      variant: "default",
    });
  };

  const handleDeleteDefinition = async (definitionId: string) => {
    if (!confirm(isRTL ? "هل أنت متأكد من حذف هذا التعريف؟" : "Are you sure you want to delete this definition?")) {
      return;
    }

    try {
      await PDPLService.deleteDefinition(definitionId);
      await loadPDPLData(); // Reload data after deletion
      toast({
        title: isRTL ? "تم حذف التعريف بنجاح" : "Definition deleted successfully",
        description: isRTL ? "تم حذف التعريف" : "Definition has been deleted",
        variant: "default",
      });
    } catch (error) {
      console.error('Error deleting definition:', error);
      toast({
        title: isRTL ? "خطأ في حذف التعريف" : "Error deleting definition",
        description: isRTL ? "فشل في حذف التعريف" : "Failed to delete definition",
        variant: "destructive",
      });
    }
  };

  const handleOpenArticleModal = (article: {
    id?: string;
    articleNumber: string;
    title: string;
    description: string;
    points?: PDPLPoint[];
    linkedArticles?: string[];
    createdAt: Timestamp | Date | string;
    updatedAt: Timestamp | Date | string;
  }, pointId?: string) => {
    setSelectedArticle(article);
    setSelectedPointId(pointId);
    setIsArticleViewModalOpen(true);
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Hero Section - Full Width */}
      <div className="relative min-h-[85vh] bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
          <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-white rounded-full opacity-50"></div>
        </div>

        <div className="relative z-10 flex flex-col justify-center items-center min-h-[85vh] px-8 py-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-6xl mx-auto"
          >
            {/* Main Header */}
            <div className="flex items-center justify-center gap-6 mb-8">
              <motion.div 
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="w-20 h-20 md:w-24 md:h-24 bg-white/20 backdrop-blur-sm rounded-3xl flex items-center justify-center shadow-2xl border border-white/30"
              >
                <Shield className="w-10 h-10 md:w-12 md:h-12 text-white" />
              </motion.div>
              <div className="text-left">
                <motion.h1 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="text-5xl md:text-7xl font-bold text-white mb-2 tracking-tight"
                >
                  {isRTL ? "تصنيف البيانات" : "Data Classification"}
                </motion.h1>
                <motion.p 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="text-xl md:text-2xl text-white/90 font-medium"
                >
                  {isRTL ? "إدارة أنظمة البيانات المؤسسية" : "Enterprise Data Systems Management"}
                </motion.p>
              </div>
            </div>

            {/* Description */}
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="text-lg md:text-xl text-white/85 max-w-4xl mx-auto mb-12 leading-relaxed font-light"
            >
              {isRTL 
                ? "منصة متطورة وشاملة لإدارة وتصنيف أنظمة البيانات في مؤسستك. استفد من أدوات قوية لإضافة الأنظمة، تعيين المسؤولين، وإدارة قواعد البيانات بأحدث التقنيات والمعايير الأمنية."
                : "An advanced and comprehensive platform for managing and classifying data systems within your organization. Leverage powerful tools to add systems, assign responsible owners, and manage databases with cutting-edge technology and security standards."
              }
            </motion.p>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1 }}
              className="mb-16"
            >
              <Button
                onClick={() => setIsModalOpen(true)}
                size="lg"
                className="bg-white text-[var(--brand-blue)] hover:bg-white/90 font-bold px-12 py-4 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 text-lg border-0"
              >
                <Plus className="w-6 h-6 mr-3" />
                {isRTL ? "إضافة نظام جديد" : "Add New System"}
              </Button>
            </motion.div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8 max-w-5xl mx-auto"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
                  className="bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 group"
                >
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="p-3 rounded-xl bg-white/20 text-white group-hover:bg-white/30 transition-all duration-300">
                        {stat.icon}
                      </div>
                    </div>
                    <p className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.value}</p>
                    <p className="text-sm md:text-base text-white/80 font-medium">{stat.title}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Floating Elements */}
        <motion.div
          animate={{ 
            y: [0, -20, 0],
            rotate: [0, 5, 0]
          }}
          transition={{ 
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-20 right-20 w-16 h-16 bg-white/10 rounded-full backdrop-blur-sm border border-white/20 hidden lg:block"
        />
        <motion.div
          animate={{ 
            y: [0, 15, 0],
            rotate: [0, -5, 0]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute bottom-32 left-16 w-12 h-12 bg-white/10 rounded-full backdrop-blur-sm border border-white/20 hidden lg:block"
        />
      </div>

      {/* Main Tab Navigation */}
      <div className="px-8 pt-8 pb-0">
        <div className="flex justify-center">
          <div className="bg-white rounded-2xl p-1 shadow-lg border border-gray-200">
            <div className="flex gap-1">
              {mainTabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-3 px-8 py-4 rounded-xl transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-[var(--brand-blue)] text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-5 h-5" />
                  <div className="text-left">
                    <div className="font-semibold">{tab.label}</div>
                    <div className="text-xs opacity-80">{tab.subtitle}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'systems' && (
        <div className="px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          {/* Section Header */}
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="inline-flex items-center gap-3 bg-[var(--brand-blue)]/10 px-6 py-3 rounded-full mb-6"
            >
              <Database className="w-5 h-5 text-[var(--brand-blue)]" />
              <span className="text-[var(--brand-blue)] font-semibold">
                {isRTL ? "إدارة الأنظمة" : "Systems Management"}
              </span>
            </motion.div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--brand-dark-gray)] mb-4">
              {isRTL ? "الأنظمة المسجلة" : "Registered Systems"}
            </h2>
            
            <div className="flex items-center justify-center gap-2 text-gray-600">
              <span className="text-2xl font-bold text-[var(--brand-blue)]">{systems.length}</span>
              <span>
                {isRTL 
                  ? `${systems.length === 1 ? 'نظام مسجل' : 'أنظمة مسجلة'}`
                  : `${systems.length === 1 ? 'System Registered' : 'Systems Registered'}`
                }
              </span>
            </div>
          </div>

          {isLoading ? (
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[...Array(6)].map((_, index) => (
                  <div
                    key={index}
                    className="bg-white/50 animate-pulse rounded-2xl h-72 border border-gray-200/50"
                  />
                ))}
              </div>
            </div>
          ) : systems.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-20"
            >
              <div className="max-w-md mx-auto">
                <div className="w-32 h-32 bg-gradient-to-br from-[var(--brand-blue)]/20 to-[var(--brand-blue)]/10 rounded-full flex items-center justify-center mx-auto mb-8">
                  <Database className="w-16 h-16 text-[var(--brand-blue)]" />
                </div>
                <h3 className="text-2xl font-bold text-[var(--brand-dark-gray)] mb-4">
                  {isRTL ? "لا توجد أنظمة مسجلة" : "No Systems Registered"}
                </h3>
                <p className="text-gray-600 mb-8 text-lg">
                  {isRTL 
                    ? "ابدأ رحلتك في إدارة البيانات بإضافة أول نظام لك"
                    : "Start your data management journey by adding your first system"
                  }
                </p>
                <Button
                  onClick={() => setIsModalOpen(true)}
                  size="lg"
                  className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  {isRTL ? "إضافة نظام جديد" : "Add New System"}
                </Button>
              </div>
            </motion.div>
          ) : (
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {systems.map((system, index) => (
                  <motion.div
                    key={system.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <SystemCard
                      system={system}
                      lang={lang}
                      onEdit={handleEditSystem}
                      onDelete={handleDeleteSystem}
                    />
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </motion.div>
        </div>
      )}

      {/* PDPL Law Tab */}
      {activeTab === 'pdpl' && (
        <div className="px-8 py-16">
          {/* PDPL Sub-tabs */}
          <div className="mb-8">
            <div className="flex justify-center">
              <div className="bg-gray-100 rounded-xl p-1">
                <div className="flex gap-1">
                  {pdplTabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActivePDPLTab(tab.id)}
                      className={`flex items-center gap-2 px-6 py-3 rounded-lg transition-all duration-300 ${
                        activePDPLTab === tab.id
                          ? 'bg-white text-[var(--brand-blue)] shadow-sm font-semibold'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <tab.icon className="w-4 h-4" />
                      <div className="text-left hidden lg:block">
                        <div className="text-sm font-medium">{tab.label}</div>
                        <div className="text-xs opacity-70">{tab.subtitle}</div>
                      </div>
                      <div className="text-sm font-medium lg:hidden">{tab.label.split(' ')[0]}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* PDPL Content */}
          <motion.div
            key={activePDPLTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Header Section */}
            <div className="text-center mb-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="inline-flex items-center gap-3 bg-[var(--brand-blue)]/10 px-6 py-3 rounded-full mb-6"
              >
                {pdplTabs.find(tab => tab.id === activePDPLTab)?.icon && 
                  React.createElement(pdplTabs.find(tab => tab.id === activePDPLTab)!.icon, {
                    className: "w-5 h-5 text-[var(--brand-blue)]"
                  })
                }
                <span className="text-[var(--brand-blue)] font-semibold">
                  {pdplTabs.find(tab => tab.id === activePDPLTab)?.label}
                </span>
              </motion.div>
              
              <h2 className="text-3xl md:text-4xl font-bold text-[var(--brand-dark-gray)] mb-4">
                {pdplTabs.find(tab => tab.id === activePDPLTab)?.subtitle}
              </h2>
              
              <div className="flex items-center justify-center gap-8 text-gray-600 mb-8">
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-[var(--brand-blue)]">{pdplArticles.length}</span>
                  <span>{isRTL ? 'مادة' : 'Articles'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-green-600">{pdplDefinitions.length}</span>
                  <span>{isRTL ? 'تعريف مشترك' : 'Shared Definitions'}</span>
                </div>
              </div>

              <div className="flex gap-4 justify-center">
                <Button
                  className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  onClick={() => setIsArticleModalOpen(true)}
                >
                  <Plus className="w-5 h-5 mr-2" />
                  {isRTL ? "إضافة مادة" : "Add Article"}
                </Button>
                <Button
                  variant="outline"
                  className="border-[var(--brand-blue)] text-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300"
                  onClick={() => setIsDefinitionModalOpen(true)}
                >
                  <Plus className="w-5 h-5 mr-2" />
                  {isRTL ? "إضافة تعريف مشترك" : "Add Shared Definition"}
                </Button>
              </div>
            </div>

            {isLoadingPDPL ? (
              <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {[...Array(6)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-white/50 animate-pulse rounded-2xl h-64 border border-gray-200/50"
                    />
                  ))}
                </div>
              </div>
            ) : (
              <div className="max-w-7xl mx-auto">
                {/* Articles Section */}
                {pdplArticles.length > 0 && (
                  <div className="mb-12">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                        <BookOpen className="w-5 h-5 text-[var(--brand-blue)]" />
                        {isRTL ? "المواد" : "Articles"} ({totalArticles})
                      </h3>

                      {/* Pagination Info */}
                      {totalArticles > articlesPerPage && (
                        <div className="text-sm text-gray-600">
                          {isRTL
                            ? `الصفحة ${currentArticlePage} من ${Math.ceil(totalArticles / articlesPerPage)}`
                            : `Page ${currentArticlePage} of ${Math.ceil(totalArticles / articlesPerPage)}`
                          }
                        </div>
                      )}
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {pdplArticles.map((article, index) => (
                        <motion.div
                          key={article.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.2, delay: index * 0.05 }} // Reduced animation delay for better performance
                        >
                          <PDPLArticleCard
                            article={article}
                            lang={lang}
                            onEdit={handleEditArticle}
                            onDelete={handleDeleteArticle}
                            onOpenModal={handleOpenArticleModal}
                          />
                        </motion.div>
                      ))}
                    </div>

                    {/* Pagination Controls */}
                    {totalArticles > articlesPerPage && (
                      <div className="flex justify-center items-center gap-4 mt-8">
                        <Button
                          variant="outline"
                          onClick={() => loadPDPLData(currentArticlePage - 1)}
                          disabled={currentArticlePage === 1}
                          className="flex items-center gap-2"
                        >
                          {isRTL ? "السابق" : "Previous"}
                        </Button>

                        <div className="flex items-center gap-2">
                          {Array.from({ length: Math.ceil(totalArticles / articlesPerPage) }, (_, i) => i + 1)
                            .filter(page =>
                              page === 1 ||
                              page === Math.ceil(totalArticles / articlesPerPage) ||
                              Math.abs(page - currentArticlePage) <= 1
                            )
                            .map((page, index, array) => (
                              <React.Fragment key={page}>
                                {index > 0 && array[index - 1] !== page - 1 && (
                                  <span className="text-gray-400">...</span>
                                )}
                                <Button
                                  variant={page === currentArticlePage ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => loadPDPLData(page)}
                                  className="w-10 h-10"
                                >
                                  {page}
                                </Button>
                              </React.Fragment>
                            ))
                          }
                        </div>

                        <Button
                          variant="outline"
                          onClick={() => loadPDPLData(currentArticlePage + 1)}
                          disabled={currentArticlePage === Math.ceil(totalArticles / articlesPerPage)}
                          className="flex items-center gap-2"
                        >
                          {isRTL ? "التالي" : "Next"}
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {/* Definitions Section - Simple View Glossary */}
                {pdplDefinitions.length > 0 && (
                  <div className="mb-12">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className="text-center"
                    >
                      <div className="inline-flex items-center gap-4 bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white px-8 py-6 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer group"
                        onClick={() => setIsGlossaryModalOpen(true)}
                      >
                        <div className="w-14 h-14 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                          <BookOpen className="w-7 h-7 text-white" />
                        </div>
                        <div className="text-left">
                          <h3 className="text-xl font-bold text-white mb-1">
                            {isRTL ? "معجم التعريفات" : "Definitions Glossary"}
                          </h3>
                          <p className="text-blue-100">
                            {isRTL ? `${pdplDefinitions.length} تعريف مشترك` : `${pdplDefinitions.length} Shared Definitions`}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                )}

                {/* Empty State */}
                {!isLoadingPDPL && pdplArticles.length === 0 && pdplDefinitions.length === 0 && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-20"
                  >
                    <div className="max-w-md mx-auto">
                      <div className="w-32 h-32 bg-gradient-to-br from-[var(--brand-blue)]/20 to-[var(--brand-blue)]/10 rounded-full flex items-center justify-center mx-auto mb-8">
                        {pdplTabs.find(tab => tab.id === activePDPLTab)?.icon && 
                          React.createElement(pdplTabs.find(tab => tab.id === activePDPLTab)!.icon, {
                            className: "w-16 h-16 text-[var(--brand-blue)]"
                          })
                        }
                      </div>
                      <h3 className="text-2xl font-bold text-[var(--brand-dark-gray)] mb-4">
                        {isRTL ? "لا توجد بيانات" : "No Data Available"}
                      </h3>
                      <p className="text-gray-600 mb-8 text-lg">
                        {isRTL 
                          ? "ابدأ بإضافة مواد لهذا القسم وتعريفات مشتركة"
                          : "Start by adding articles for this section and shared definitions"
                        }
                      </p>
                    </div>
                  </motion.div>
                )}
              </div>
            )}
          </motion.div>
        </div>
      )}

      {/* Add System Modal */}
      <AddSystemModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleAddSystem}
        lang={lang}
        isLoading={isSubmitting}
      />

      {/* PDPL Article Modal */}
      <AddPDPLArticleModal
        isOpen={isArticleModalOpen}
        onClose={() => setIsArticleModalOpen(false)}
        onSubmit={handleAddArticle}
        lang={lang}
        isLoading={isSubmittingPDPL}
        documentType={activePDPLTab}
      />

      {/* PDPL Definition Modal */}
      <AddPDPLDefinitionModal
        isOpen={isDefinitionModalOpen}
        onClose={() => setIsDefinitionModalOpen(false)}
        onSubmit={handleAddDefinition}
        lang={lang}
        isLoading={isSubmittingPDPL}
      />

      {/* PDPL Glossary Modal */}
      <PDPLGlossaryModal
        isOpen={isGlossaryModalOpen}
        onClose={() => setIsGlossaryModalOpen(false)}
        definitions={pdplDefinitions}
        lang={lang}
        onEdit={handleEditDefinition}
        onDelete={handleDeleteDefinition}
      />

      {/* Article View Modal */}
      {selectedArticle && (
        <ArticleModal
          isOpen={isArticleViewModalOpen}
          onClose={() => {
            setIsArticleViewModalOpen(false);
            setSelectedArticle(null);
            setSelectedPointId(undefined);
          }}
          article={selectedArticle}
          lang={lang}
          selectedPointId={selectedPointId}
        />
      )}

      {/* Edit System Modal */}
      {editModalOpen && editingSystem && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Edit className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">
                      {isRTL ? "تعديل النظام" : "Edit System"}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {isRTL ? "تحديث معلومات النظام" : "Update system information"}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setEditModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg p-2 transition-all duration-200"
                  disabled={isSubmitting}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Form */}
              <div className="space-y-4">
                {/* System Name */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    <Building className="w-4 h-4 inline mr-2" />
                    {isRTL ? "اسم النظام" : "System Name"}
                  </label>
                  <input
                    type="text"
                    value={editFormData.name}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent"
                    placeholder={isRTL ? "أدخل اسم النظام" : "Enter system name"}
                    disabled={isSubmitting}
                  />
                </div>

                {/* Responsible Owner */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    <User className="w-4 h-4 inline mr-2" />
                    {isRTL ? "المسؤول عن النظام" : "Responsible Owner"}
                  </label>
                  <input
                    type="text"
                    value={editFormData.responsibleOwner}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, responsibleOwner: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent"
                    placeholder={isRTL ? "أدخل اسم المسؤول" : "Enter responsible owner"}
                    disabled={isSubmitting}
                  />
                </div>

                {/* DBA */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    <Database className="w-4 h-4 inline mr-2" />
                    {isRTL ? "مدير قاعدة البيانات" : "Database Administrator"}
                  </label>
                  <input
                    type="text"
                    value={editFormData.dba}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, dba: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent"
                    placeholder={isRTL ? "أدخل اسم مدير قاعدة البيانات" : "Enter DBA name"}
                    disabled={isSubmitting}
                  />
                </div>

                {/* Email */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    <Mail className="w-4 h-4 inline mr-2" />
                    {isRTL ? "البريد الإلكتروني" : "Email"}
                  </label>
                  <input
                    type="email"
                    value={editFormData.email}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent"
                    placeholder={isRTL ? "أدخل البريد الإلكتروني" : "Enter email address"}
                    disabled={isSubmitting}
                  />
                </div>

                {/* Group */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    <Building className="w-4 h-4 inline mr-2" />
                    {isRTL ? "المجموعة" : "Group"}
                  </label>
                  <select
                    value={editFormData.group}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, group: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    <option value="">{isRTL ? "اختر المجموعة" : "Select Group"}</option>
                    {Array.from({ length: 9 }, (_, i) => i + 1).map(num => (
                      <option key={num} value={num.toString()}>
                        {isRTL ? `المجموعة ${num}` : `Group ${num}`}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Consultant */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    <User className="w-4 h-4 inline mr-2" />
                    {isRTL ? "المستشار المسؤول" : "Assigned Consultant"}
                  </label>
                  <select
                    value={editFormData.consultantId}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, consultantId: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--brand-blue)] focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
                    disabled={isSubmitting || loadingConsultants}
                  >
                    <option value="">
                      {loadingConsultants
                        ? (isRTL ? "جاري التحميل..." : "Loading...")
                        : (isRTL ? "بدون مستشار" : "No Consultant")
                      }
                    </option>
                    {consultants.map((consultant) => (
                      <option key={consultant.uid} value={consultant.uid}>
                        {consultant.displayName || consultant.email}
                        {consultant.email && consultant.displayName && ` (${consultant.email})`}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-6 border-t border-gray-200 mt-6">
                <Button
                  onClick={() => setEditModalOpen(false)}
                  variant="outline"
                  className="flex-1"
                  disabled={isSubmitting}
                >
                  {isRTL ? "إلغاء" : "Cancel"}
                </Button>
                <Button
                  onClick={handleSaveSystemEdit}
                  className="flex-1 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                  disabled={isSubmitting || !editFormData.name.trim()}
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      {isRTL ? "جاري الحفظ..." : "Saving..."}
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      {isRTL ? "حفظ التغييرات" : "Save Changes"}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteModalOpen && deletingSystem && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-2xl shadow-2xl max-w-md w-full"
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                  <Trash2 className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">
                    {isRTL ? "تأكيد حذف النظام" : "Confirm System Deletion"}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {isRTL ? "هذا الإجراء لا يمكن التراجع عنه" : "This action cannot be undone"}
                  </p>
                </div>
              </div>

              {/* Warning */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <p className="text-red-800 text-sm">
                  {isRTL
                    ? `سيتم حذف النظام "${deletingSystem.name}" وجميع البيانات المرتبطة به نهائياً.`
                    : `System "${deletingSystem.name}" and all associated data will be permanently deleted.`
                  }
                </p>
              </div>

              {/* Confirmation Input */}
              <div className="space-y-2 mb-6">
                <label className="block text-sm font-semibold text-gray-700">
                  {isRTL
                    ? `اكتب "delete ${deletingSystem.name}" للتأكيد:`
                    : `Type "delete ${deletingSystem.name}" to confirm:`
                  }
                </label>
                <input
                  type="text"
                  value={deleteConfirmText}
                  onChange={(e) => setDeleteConfirmText(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder={`delete ${deletingSystem.name}`}
                  disabled={isDeleting}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button
                  onClick={() => setDeleteModalOpen(false)}
                  variant="outline"
                  className="flex-1"
                  disabled={isDeleting}
                >
                  {isRTL ? "إلغاء" : "Cancel"}
                </Button>
                <Button
                  onClick={handleConfirmDelete}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                  disabled={isDeleting || deleteConfirmText.toLowerCase() !== `delete ${deletingSystem.name}`.toLowerCase()}
                >
                  {isDeleting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      {isRTL ? "جاري الحذف..." : "Deleting..."}
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      {isRTL ? "حذف النظام" : "Delete System"}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}